# 计算器应用

一个使用Flutter开发的多平台计算器应用，支持Android、iOS、Windows、macOS、Linux和Web平台。

## 功能特性

- 🧮 基本计算功能：加、减、乘、除
- 🔄 正负号切换
- 📊 百分比计算
- 🎨 现代化UI设计
- 🌙 支持深色模式
- 📱 响应式布局，适配各种屏幕尺寸
- 🌍 跨平台支持

## 支持的平台

- ✅ Android
- ✅ iOS  
- ✅ Windows
- ✅ macOS
- ✅ Linux
- ✅ Web

## 运行要求

- Flutter SDK 3.0.0 或更高版本
- Dart SDK 3.0.0 或更高版本

## 安装和运行

1. 确保已安装Flutter SDK
2. 克隆或下载项目
3. 在项目根目录运行以下命令：

```bash
# 获取依赖
flutter pub get

# 运行应用（默认平台）
flutter run

# 运行在特定平台
flutter run -d android    # Android
flutter run -d ios        # iOS
flutter run -d windows    # Windows
flutter run -d macos      # macOS
flutter run -d linux      # Linux
flutter run -d chrome     # Web
```

## 构建发布版本

```bash
# Android APK
flutter build apk

# Android App Bundle
flutter build appbundle

# iOS
flutter build ios

# Windows
flutter build windows

# macOS
flutter build macos

# Linux
flutter build linux

# Web
flutter build web
```

## 项目结构

```
lib/
├── main.dart              # 应用入口
├── calculator_screen.dart  # 主界面
└── calculator_logic.dart   # 计算逻辑
```

## 技术栈

- **框架**: Flutter
- **语言**: Dart
- **UI**: Material Design 3
- **状态管理**: StatefulWidget

## 许可证

MIT License

