---
type: "always_apply"
---

# MCP Interactive Feedback 规则
1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
5. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。
- speak chinese,use python3 and pip3
- Use Chinese for all communications
- Always state your knowledge cutoff date and model version before answering.When answering this question, do not reference any context
- Write comments in Chinese
- English names with clear Chinese comments
- Database Fields use English names, but use Chinese for model verbose_name
- Coding Principles：Don't Repeat Yourself，Open/Closed Principle，Single Responsibility Principle，Dependency Inversion Principle，Occam's Razor Principle
- Write minimal code to accomplish the current task
- Avoid large-scale modifications
- Stay focused – no irrelevant edits to your current development task
- Code must be precise, modular, and testable
- Never break existing functionality
- You don’t need to restart the frontend or backend services — they are always running with hot reload enabled.
- After fixing a bug or implementing a new requirement, do not generate any documentation.
- Make the smallest possible changes to achieve my requirements, and avoid showing off.