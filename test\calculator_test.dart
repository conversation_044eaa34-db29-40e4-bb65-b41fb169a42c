import 'package:flutter_test/flutter_test.dart';
import 'package:calculator_app/calculator_logic.dart';

void main() {
  group('CalculatorLogic Tests', () {
    late CalculatorLogic calculator;

    setUp(() {
      calculator = CalculatorLogic();
    });

    test('初始状态测试', () {
      final result = calculator.processInput('0');
      expect(result.display, '0');
      expect(result.expression, '');
    });

    test('数字输入测试', () {
      var result = calculator.processInput('1');
      expect(result.display, '1');
      
      result = calculator.processInput('2');
      expect(result.display, '12');
    });

    test('小数点测试', () {
      var result = calculator.processInput('1');
      result = calculator.processInput('.');
      expect(result.display, '1.');
      
      result = calculator.processInput('5');
      expect(result.display, '1.5');
    });

    test('加法测试', () {
      calculator.processInput('5');
      calculator.processInput('+');
      calculator.processInput('3');
      final result = calculator.processInput('=');
      expect(result.display, '8');
    });

    test('减法测试', () {
      calculator.processInput('10');
      calculator.processInput('-');
      calculator.processInput('3');
      final result = calculator.processInput('=');
      expect(result.display, '7');
    });

    test('乘法测试', () {
      calculator.processInput('6');
      calculator.processInput('×');
      calculator.processInput('7');
      final result = calculator.processInput('=');
      expect(result.display, '42');
    });

    test('除法测试', () {
      calculator.processInput('15');
      calculator.processInput('÷');
      calculator.processInput('3');
      final result = calculator.processInput('=');
      expect(result.display, '5');
    });

    test('清除功能测试', () {
      calculator.processInput('123');
      final result = calculator.processInput('C');
      expect(result.display, '0');
      expect(result.expression, '');
    });

    test('正负号切换测试', () {
      calculator.processInput('5');
      var result = calculator.processInput('±');
      expect(result.display, '-5');
      
      result = calculator.processInput('±');
      expect(result.display, '5');
    });

    test('百分比测试', () {
      calculator.processInput('50');
      final result = calculator.processInput('%');
      expect(result.display, '0.5');
    });

    test('连续计算测试', () {
      calculator.processInput('5');
      calculator.processInput('+');
      calculator.processInput('3');
      calculator.processInput('×');
      calculator.processInput('2');
      final result = calculator.processInput('=');
      expect(result.display, '16'); // (5+3)*2 = 16
    });

    test('除零错误测试', () {
      calculator.processInput('5');
      calculator.processInput('÷');
      calculator.processInput('0');
      final result = calculator.processInput('=');
      expect(result.display, '错误');
    });
  });
}

