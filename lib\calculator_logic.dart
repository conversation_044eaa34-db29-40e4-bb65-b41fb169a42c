class CalculatorResult {
  final String display;
  final String expression;

  CalculatorResult({required this.display, required this.expression});
}

class CalculatorLogic {
  String _currentNumber = '0';
  String _previousNumber = '';
  String _operation = '';
  bool _shouldResetDisplay = false;
  String _expression = '';

  CalculatorResult processInput(String input) {
    switch (input) {
      case '0':
      case '1':
      case '2':
      case '3':
      case '4':
      case '5':
      case '6':
      case '7':
      case '8':
      case '9':
        return _handleNumber(input);
      case '.':
        return _handleDecimal();
      case '+':
      case '-':
      case '×':
      case '÷':
        return _handleOperator(input);
      case '=':
        return _handleEquals();
      case 'C':
        return _handleClear();
      case '±':
        return _handlePlusMinus();
      case '%':
        return _handlePercentage();
      default:
        return CalculatorResult(display: _currentNumber, expression: _expression);
    }
  }

  CalculatorResult _handleNumber(String number) {
    if (_shouldResetDisplay) {
      _currentNumber = number;
      _shouldResetDisplay = false;
    } else {
      if (_currentNumber == '0') {
        _currentNumber = number;
      } else {
        _currentNumber += number;
      }
    }
    _updateExpression();
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  CalculatorResult _handleDecimal() {
    if (_shouldResetDisplay) {
      _currentNumber = '0.';
      _shouldResetDisplay = false;
    } else if (!_currentNumber.contains('.')) {
      _currentNumber += '.';
    }
    _updateExpression();
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  CalculatorResult _handleOperator(String operator) {
    if (_currentNumber.isNotEmpty && _currentNumber != '0') {
      if (_previousNumber.isNotEmpty && _operation.isNotEmpty) {
        _calculate();
      }
      _previousNumber = _currentNumber;
      _operation = operator;
      _shouldResetDisplay = true;
      _updateExpression();
    } else if (_operation.isNotEmpty) {
      _operation = operator;
      _updateExpression();
    }
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  CalculatorResult _handleEquals() {
    if (_previousNumber.isNotEmpty && _operation.isNotEmpty && _currentNumber.isNotEmpty) {
      _calculate();
      _previousNumber = '';
      _operation = '';
      _shouldResetDisplay = true;
      _expression = '';
    }
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  CalculatorResult _handleClear() {
    _currentNumber = '0';
    _previousNumber = '';
    _operation = '';
    _shouldResetDisplay = false;
    _expression = '';
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  CalculatorResult _handlePlusMinus() {
    if (_currentNumber != '0') {
      if (_currentNumber.startsWith('-')) {
        _currentNumber = _currentNumber.substring(1);
      } else {
        _currentNumber = '-$_currentNumber';
      }
      _updateExpression();
    }
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  CalculatorResult _handlePercentage() {
    if (_currentNumber != '0') {
      double number = double.parse(_currentNumber);
      number = number / 100;
      _currentNumber = _formatNumber(number);
      _updateExpression();
    }
    return CalculatorResult(display: _currentNumber, expression: _expression);
  }

  void _calculate() {
    double prev = double.parse(_previousNumber);
    double current = double.parse(_currentNumber);
    double result = 0;

    switch (_operation) {
      case '+':
        result = prev + current;
        break;
      case '-':
        result = prev - current;
        break;
      case '×':
        result = prev * current;
        break;
      case '÷':
        if (current != 0) {
          result = prev / current;
        } else {
          _currentNumber = '错误';
          return;
        }
        break;
    }

    _currentNumber = _formatNumber(result);
  }

  String _formatNumber(double number) {
    if (number == number.toInt()) {
      return number.toInt().toString();
    } else {
      return number.toString();
    }
  }

  void _updateExpression() {
    _expression = '';
    if (_previousNumber.isNotEmpty) {
      _expression += _previousNumber;
    }
    if (_operation.isNotEmpty) {
      _expression += ' $_operation ';
    }
    if (_currentNumber.isNotEmpty && !_shouldResetDisplay) {
      _expression += _currentNumber;
    }
  }
}

